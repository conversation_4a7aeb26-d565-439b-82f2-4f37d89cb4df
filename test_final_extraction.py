#!/usr/bin/env python3
"""
测试最终的订单数据抓取功能（包含Shadow DOM支持）
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from playwright.async_api import async_playwright
import requests

async def test_final_order_extraction():
    """测试最终的订单抓取功能"""
    
    print("🔍 测试最终的订单数据抓取功能（Shadow DOM支持）")
    print("=" * 60)
    
    # 检查Chrome调试接口
    debug_port = 9222
    try:
        version_url = f"http://localhost:{debug_port}/json/version"
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        if response.status_code != 200:
            print(f"❌ Chrome调试接口不可用，状态码: {response.status_code}")
            print("💡 请先运行GUI程序或手动启动Chrome调试模式")
            return
        
        version_info = response.json()
        ws_url = version_info.get('webSocketDebuggerUrl')
        if not ws_url:
            print("❌ 无法获取WebSocket URL")
            return
        
        print(f"✅ Chrome调试接口正常: {version_info.get('Browser', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ Chrome调试接口连接失败: {e}")
        print("💡 请确保Chrome调试模式正在运行")
        return
    
    # 连接到Chrome
    async with async_playwright() as p:
        try:
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例!")
            
            # 查找1688页面
            page = None
            if browser.contexts and browser.contexts[0].pages:
                for p_page in browser.contexts[0].pages:
                    if '1688.com' in p_page.url:
                        page = p_page
                        print(f"📄 找到1688页面: {p_page.url}")
                        break
            
            if not page:
                print("❌ 未找到1688页面")
                print("💡 请在Chrome中打开1688网站并登录到订单页面")
                return
            
            # 执行完整的订单提取测试
            await test_complete_extraction(page)
            
            await browser.close()
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")

async def test_complete_extraction(page):
    """测试完整的订单提取流程"""
    print("\n🔍 测试完整的订单提取流程")
    print("-" * 40)
    
    try:
        # 执行我们改进的JavaScript代码
        result = await page.evaluate("""
            () => {
                const toText = el => (el?.textContent || "").trim();
                const toNumber = str => (
                    parseFloat(String(str).replace(/[^\\d.]/g, "")) || 0
                );

                console.log("🔍 开始完整的订单数据提取...");

                // Shadow DOM 穿透函数
                function querySelectorDeep(selector, root = document) {
                    const elements = [];
                    
                    // 在当前层级查找
                    const found = root.querySelectorAll(selector);
                    elements.push(...found);
                    
                    // 递归查找所有 Shadow DOM
                    const allElements = root.querySelectorAll('*');
                    for (const el of allElements) {
                        if (el.shadowRoot) {
                            const shadowElements = querySelectorDeep(selector, el.shadowRoot);
                            elements.push(...shadowElements);
                        }
                    }
                    
                    return elements;
                }

                // 查找订单项
                console.log("🔍 查找订单项（支持Shadow DOM）...");
                const selectors = [
                    'order-item',
                    'order-item[data-tracker]',
                    '[data-tracker*="order"]'
                ];

                let items = [];
                let usedSelector = '';
                
                for (const selector of selectors) {
                    const found = querySelectorDeep(selector);
                    console.log(`Shadow DOM查找 ${selector}: 找到 ${found.length} 个元素`);
                    
                    if (found.length > 0) {
                        items = found;
                        usedSelector = selector;
                        console.log(`✅ 使用选择器: ${selector} (找到 ${found.length} 个元素)`);
                        break;
                    }
                }
                
                if (items.length === 0) {
                    console.log("❌ 未找到任何订单项");
                    return { success: false, error: "未找到订单项", items: [] };
                }

                console.log(`🎯 开始提取 ${items.length} 个订单的详细信息...`);

                // 提取订单详细信息
                const orders = items.map((item, index) => {
                    console.log(`处理第 ${index + 1} 个订单...`);
                    
                    // 提取标题
                    let title = "";
                    const titleSelectors = [
                        '.title, .product-title, .goods-title, .product-name, .goods-name',
                        '[class*="title"], [class*="name"], [class*="product"]',
                        'a[href*="offer"], a[href*="detail"]',
                        'h1, h2, h3, h4, h5, h6'
                    ];
                    
                    for (const selector of titleSelectors) {
                        const el = item.querySelector(selector);
                        if (el && toText(el) && toText(el).length > 5) {
                            title = toText(el);
                            break;
                        }
                    }
                    
                    // 如果没找到，尝试从文本中提取
                    if (!title) {
                        const itemText = toText(item);
                        const lines = itemText.split('\\n').map(line => line.trim()).filter(line => line.length > 5);
                        title = lines.reduce((longest, current) => 
                            current.length > longest.length ? current : longest, '');
                    }

                    // 提取价格
                    let price = 0;
                    const priceSelectors = [
                        '.price, .amount, .money, .unit-price',
                        '[class*="price"], [class*="amount"], [class*="money"]',
                        '[class*="cost"], [class*="fee"]'
                    ];
                    
                    for (const selector of priceSelectors) {
                        const el = item.querySelector(selector);
                        if (el && toText(el)) {
                            const priceText = toText(el);
                            if (priceText.includes('￥') || priceText.includes('¥') || priceText.includes('元')) {
                                price = toNumber(priceText);
                                if (price > 0) break;
                            }
                        }
                    }
                    
                    // 如果没找到，从整个文本中提取价格
                    if (price === 0) {
                        const itemText = toText(item);
                        const priceMatch = itemText.match(/[￥¥]([\\d,]+\\.?\\d*)|([\\d,]+\\.?\\d*)元/);
                        if (priceMatch) {
                            price = toNumber(priceMatch[1] || priceMatch[2]);
                        }
                    }

                    // 提取数量
                    let quantity = 1;
                    const quantitySelectors = [
                        '.quantity, .num, .count, .number',
                        '[class*="quantity"], [class*="num"], [class*="count"]',
                        '[class*="qty"]'
                    ];
                    
                    for (const selector of quantitySelectors) {
                        const el = item.querySelector(selector);
                        if (el && toText(el)) {
                            const qtyText = toText(el);
                            const qtyNum = parseInt(qtyText) || 0;
                            if (qtyNum > 0) {
                                quantity = qtyNum;
                                break;
                            }
                        }
                    }

                    // 提取data-tracker信息
                    const dataTracker = item.getAttribute('data-tracker') || '';

                    const result = {
                        title: title,
                        price: price,
                        quantity: quantity,
                        total_amount: price * quantity,
                        data_tracker: dataTracker,
                        order_id: dataTracker.split('_')[0] || '',
                        shop_name: '',
                        order_time: '',
                        status: '',
                        image_url: '',
                        detail_url: ''
                    };
                    
                    console.log(`订单 ${index + 1} 提取结果:`, {
                        title: result.title.substring(0, 30) + '...',
                        price: result.price,
                        quantity: result.quantity,
                        dataTracker: result.data_tracker.substring(0, 30) + '...'
                    });
                    
                    return result;
                }).filter(item => item.title || item.price > 0);

                console.log(`🎉 提取完成，有效订单: ${orders.length} 个`);
                
                return {
                    success: true,
                    usedSelector: usedSelector,
                    totalItems: items.length,
                    validOrders: orders.length,
                    orders: orders
                };
            }
        """)
        
        # 显示结果
        print("📊 提取结果:")
        if result['success']:
            print(f"   ✅ 提取成功")
            print(f"   🔍 使用选择器: {result['usedSelector']}")
            print(f"   📦 总订单项: {result['totalItems']} 个")
            print(f"   ✅ 有效订单: {result['validOrders']} 个")
            
            if result['orders']:
                print("\n   📋 订单详情:")
                for i, order in enumerate(result['orders'][:5]):  # 只显示前5个
                    print(f"      订单 {i+1}:")
                    print(f"         标题: {order['title'][:50]}...")
                    print(f"         价格: ¥{order['price']}")
                    print(f"         数量: {order['quantity']}")
                    print(f"         总额: ¥{order['total_amount']}")
                    if order['data_tracker']:
                        print(f"         追踪ID: {order['data_tracker'][:30]}...")
                    print()
        else:
            print(f"   ❌ 提取失败: {result['error']}")
        
    except Exception as e:
        print(f"❌ 测试完整提取失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_final_order_extraction())
