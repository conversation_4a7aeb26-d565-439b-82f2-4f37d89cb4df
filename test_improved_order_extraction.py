#!/usr/bin/env python3
"""
测试改进后的订单数据抓取功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.core.extract_orders import OrderDataExtractor

async def test_improved_order_extraction():
    """测试改进后的订单抓取功能"""
    
    print("🧪 测试改进后的订单数据抓取功能")
    print("=" * 50)
    
    # 创建提取器
    extractor = OrderDataExtractor()
    
    # 模拟页面对象
    class MockPage:
        def __init__(self):
            self.url = "https://air.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html"
            self.response_handlers = []
        
        async def title(self):
            return "1688订单列表"
        
        async def add_style_tag(self, content):
            print(f"   添加样式: {content[:30]}...")
        
        async def evaluate(self, script):
            print(f"   执行JavaScript脚本...")
            # 模拟返回一些测试数据
            if "console.log" in script:
                return [
                    {
                        'title': '测试商品1',
                        'price': 99.99,
                        'quantity': 2,
                        'shop_name': '测试店铺1',
                        'order_time': '2024-01-01',
                        'status': '待收货',
                        'image_url': '',
                        'detail_url': '',
                        'order_id': 'ORDER001',
                        'total_amount': 199.98
                    },
                    {
                        'title': '测试商品2',
                        'price': 50.0,
                        'quantity': 1,
                        'shop_name': '测试店铺2',
                        'order_time': '2024-01-02',
                        'status': '已完成',
                        'image_url': '',
                        'detail_url': '',
                        'order_id': 'ORDER002',
                        'total_amount': 50.0
                    }
                ]
            return []
        
        async def wait_for_timeout(self, ms):
            print(f"   等待 {ms}ms")
        
        async def reload(self, wait_until=None):
            print(f"   刷新页面 (wait_until={wait_until})")
        
        async def query_selector_all(self, selector):
            print(f"   查询选择器: {selector}")
            # 模拟找到一些元素
            if 'button' in selector and 'search' in selector:
                return [MockElement()]
            return []
        
        def locator(self, selector):
            return MockLocator()
        
        def on(self, event, handler):
            print(f"   监听事件: {event}")
            self.response_handlers.append(handler)
    
    class MockElement:
        async def text_content(self):
            return "测试元素文本"
        
        async def is_visible(self):
            return True
        
        async def is_enabled(self):
            return True
        
        async def click(self):
            print("   点击元素")
    
    class MockLocator:
        @property
        def first(self):
            return MockElement()
    
    # 测试API抓取
    print("\n1️⃣ 测试API抓取功能")
    mock_page = MockPage()
    
    try:
        api_orders = await extractor.extract_orders_from_api(mock_page)
        print(f"   API抓取结果: {len(api_orders)} 个订单")
        
        # 测试DOM解析
        print("\n2️⃣ 测试DOM解析功能")
        dom_orders = await extractor.extract_orders_from_dom(mock_page)
        print(f"   DOM解析结果: {len(dom_orders)} 个订单")
        
        if dom_orders:
            print("   订单详情:")
            for i, order in enumerate(dom_orders):
                print(f"     订单 {i+1}: {order['title']} - ¥{order['price']} x {order['quantity']}")
        
        # 测试单页抓取
        print("\n3️⃣ 测试单页抓取功能")
        single_page_orders = await extractor.extract_single_page_orders(mock_page)
        print(f"   单页抓取结果: {len(single_page_orders)} 个订单")
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_improved_order_extraction())
