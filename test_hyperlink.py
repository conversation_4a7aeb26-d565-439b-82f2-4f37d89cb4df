#!/usr/bin/env python3
"""
测试超链接功能
"""

import re
import os

def enhance_message_with_links(msg):
    """将消息中的文件路径转换为可点击的超链接"""
    import re
    import os

    # 匹配文件路径的正则表达式 - 优先匹配绝对路径，避免重叠
    # 匹配 Windows 绝对路径格式，如 D:\path\to\file.xlsx
    absolute_pattern = r'([A-Za-z]:[\\\/][^<>\s]+\.(?:xlsx|xls|json|csv|txt))'

    enhanced_msg = msg

    # 首先处理绝对路径
    matches = list(re.finditer(absolute_pattern, enhanced_msg))
    print(f"找到 {len(matches)} 个匹配的路径")
    
    for i, match in enumerate(matches):
        file_path = match.group(1)
        print(f"匹配 {i+1}: {file_path}")
        print(f"文件是否存在: {os.path.exists(file_path)}")
    
    for match in reversed(matches):  # 从后往前替换，避免位置偏移
        file_path = match.group(1)
        start, end = match.span(1)

        # 确保路径存在
        if os.path.exists(file_path):
            # 创建可点击的超链接
            file_name = os.path.basename(file_path)

            # 确保使用绝对路径
            if not os.path.isabs(file_path):
                abs_file_path = os.path.abspath(file_path)
                abs_dir_path = os.path.dirname(abs_file_path)
            else:
                abs_file_path = file_path
                abs_dir_path = os.path.dirname(file_path)

            # 将Windows路径转换为URL格式（使用正斜杠）
            file_url = abs_file_path.replace('\\', '/')
            dir_url = abs_dir_path.replace('\\', '/')

            # 创建超链接HTML
            link_html = (
                f'<a href="file:///{file_url}" '
                f'style="color: #2196F3; text-decoration: underline;" '
                f'title="点击打开文件: {abs_file_path}">'
                f'{file_name}</a> '
                f'<a href="file:///{dir_url}" '
                f'style="color: #4CAF50; text-decoration: none; font-size: 0.9em;" '
                f'title="点击打开目录: {abs_dir_path}">'
                f'📁</a>'
            )

            # 替换原始路径
            enhanced_msg = enhanced_msg[:start] + link_html + enhanced_msg[end:]

    # 然后处理相对路径（只有在没有被绝对路径处理过的情况下）
    if '<a href=' not in enhanced_msg:  # 如果还没有超链接
        relative_patterns = [
            r'(reports[\\\/][^<>\s]+\.(?:xlsx|xls|json|csv|txt))',     # reports目录相对路径
            r'(data[\\\/][^<>\s]+\.(?:xlsx|xls|json|csv|txt))',        # data目录相对路径
        ]

        for pattern in relative_patterns:
            matches = list(re.finditer(pattern, enhanced_msg))
            for match in reversed(matches):
                file_path = match.group(1)
                start, end = match.span(1)

                # 确保路径存在
                if os.path.exists(file_path):
                    # 创建可点击的超链接
                    file_name = os.path.basename(file_path)

                    # 对于相对路径，转换为绝对路径
                    if not os.path.isabs(file_path):
                        abs_file_path = os.path.abspath(file_path)
                        abs_dir_path = os.path.dirname(abs_file_path)
                    else:
                        abs_file_path = file_path
                        abs_dir_path = os.path.dirname(file_path)

                    # 将Windows路径转换为URL格式（使用正斜杠）
                    file_url = abs_file_path.replace('\\', '/')
                    dir_url = abs_dir_path.replace('\\', '/')

                    # 创建超链接HTML
                    link_html = (
                        f'<a href="file:///{file_url}" '
                        f'style="color: #2196F3; text-decoration: underline;" '
                        f'title="点击打开文件: {abs_file_path}">'
                        f'{file_name}</a> '
                        f'<a href="file:///{dir_url}" '
                        f'style="color: #4CAF50; text-decoration: none; font-size: 0.9em;" '
                        f'title="点击打开目录: {abs_dir_path}">'
                        f'📁</a>'
                    )

                    # 替换原始路径
                    enhanced_msg = enhanced_msg[:start] + link_html + enhanced_msg[end:]
                    break  # 只处理第一个匹配的相对路径

    return enhanced_msg

def test_hyperlink():
    """测试超链接功能"""
    
    # 测试消息
    test_messages = [
        "输出文件: D:\\1688_automation_project\\reports\\1688_orders_20250812_185849.xlsx",
        "💾 数据已保存到: D:\\1688_automation_project\\reports\\1688_orders_20250812_185849.xlsx",
        "📄 输出文件: D:\\1688_automation_project\\reports\\1688_orders_20250812_185849.xlsx"
    ]
    
    # 创建测试文件
    test_file = "D:\\1688_automation_project\\reports\\test_file.xlsx"
    os.makedirs(os.path.dirname(test_file), exist_ok=True)
    
    # 创建一个空文件用于测试
    with open(test_file, 'w') as f:
        f.write("")
    
    print("🧪 测试超链接功能")
    print("=" * 50)
    
    for i, msg in enumerate(test_messages):
        print(f"\n测试消息 {i+1}: {msg}")
        enhanced = enhance_message_with_links(msg)
        print(f"增强后: {enhanced}")
        print(f"是否包含超链接: {'是' if '<a href=' in enhanced else '否'}")
    
    # 清理测试文件
    try:
        os.remove(test_file)
    except:
        pass

if __name__ == "__main__":
    test_hyperlink()
