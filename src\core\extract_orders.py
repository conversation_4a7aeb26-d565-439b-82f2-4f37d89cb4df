#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688订单数据抓取工具
支持从1688订单页面抓取订单数据，包括接口法和DOM法
"""

import asyncio
import time
import pandas as pd
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright
import re
import sys
import requests
from pathlib import Path

# Windows下设置UTF-8输出
if sys.platform.startswith('win'):
    import codecs
    try:
        if hasattr(sys.stdout, 'detach'):
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
        else:
            # 如果detach方法不可用，使用更安全的方法
            import os
            os.environ['PYTHONIOENCODING'] = 'utf-8'
    except:
        # 如果所有方法都失败，忽略错误
        pass

# 定义项目根目录
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

class OrderDataExtractor:
    """订单数据提取器"""

    def __init__(self):
        self.order_data = []
        self.stats = {
            'total_orders': 0,
            'api_success': 0,
            'dom_success': 0,
            'failed': 0
        }

    async def extract_orders_from_api(self, page) -> List[Dict[str, Any]]:
        """通过API接口抓取订单数据"""
        orders_data = []

        def handle_response(response):
            """处理网络响应"""
            url = response.url
            # 检查是否是订单相关的API请求
            if (any(keyword in url for keyword in ['/order', '/trade', '/buyer']) and
                    response.request.resource_type == "xhr"):
                try:
                    data = response.json()
                    print(f"📡 捕获到订单API: {url}")

                    # 解析不同结构的订单数据
                    orders = self._parse_api_response(data)
                    if orders:
                        orders_data.extend(orders)
                        self.stats['api_success'] += len(orders)

                except (ValueError, TypeError, KeyError) as e:
                    print(f"⚠️ 解析API响应失败: {e}")

        # 监听响应
        page.on("response", handle_response)

        return orders_data

    def _parse_api_response(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析API响应数据"""
        if not data or not isinstance(data, dict):
            print(f"⚠️ 无效的API响应数据: "
                  f"{type(data)}")
            return []

        orders = []

        # 常见的订单数据结构路径
        possible_paths = [
            ['data', 'result', 'list'],
            ['data', 'list'],
            ['result', 'data'],
            ['data', 'orderList'],
            ['orderList'],
            ['list']
        ]

        def find_nested_value(obj, path):
            """查找嵌套值"""
            try:
                current = obj
                for key in path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        return None
                return current
            except ValueError as e:
                print(f"⚠️ 查找嵌套值失败: {e}")
                return None

        for path in possible_paths:
            try:
                order_list = find_nested_value(data, path)
                if order_list and isinstance(order_list, list):
                    print(f"✅ 找到订单列表，路径: {' -> '.join(path)}, "
                          f"数量: {len(order_list)}")

                    for i, item in enumerate(order_list):
                        if not isinstance(item, dict):
                            print(f"⚠️ 订单项 {i} 不是字典类型: "
                                  f"{type(item)}")
                            continue

                        order = self._extract_order_fields(item)
                        if order:
                            orders.append(order)
                        else:
                            print(f"⚠️ 订单项 {i} 提取失败")

                    break
                elif order_list is not None:
                    print(f"⚠️ 路径 {' -> '.join(path)} 存在但不是列表: "
                          f"{type(order_list)}")
            except (ValueError, TypeError) as e:
                print(f"⚠️ 处理路径 {' -> '.join(path)} 失败: "
                      f"{e}")
                continue

        if not orders:
            print("⚠️ 未找到有效的订单数据")
            # 输出数据结构以便调试
            data_keys = list(data.keys()) if isinstance(data, dict) else type(data)
            print(f"🔍 数据结构: {data_keys}")

        return orders

    def _extract_order_fields(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从API数据中提取订单字段"""
        try:
            # 输入验证
            if not item or not isinstance(item, dict):
                print(f"⚠️ 无效的输入数据类型: {type(item)}")
                return None

            if not item:
                print("⚠️ 空的输入数据")
                return None

            # 常见的字段映射
            field_mapping = {
                'order_id': ['orderId', 'id', 'orderNo', 'orderCode'],
                'title': ['title', 'subject', 'productName', 'name', 'goodsName'],
                'price': ['price', 'amount', 'unitPrice', 'money'],
                'quantity': ['quantity', 'num', 'count', 'number'],
                'total_amount': ['totalAmount', 'totalMoney', 'sumMoney'],
                'shop_name': ['shopName', 'sellerName', 'companyName', 'storeName'],
                'order_time': ['createTime', 'orderTime', 'gmtCreate', 'time'],
                'status': ['status', 'orderStatus', 'state', 'tradeStatus'],
                'image_url': ['picUrl', 'imageUrl', 'imgUrl', 'imagePath'],
                'detail_url': ['detailUrl', 'url', 'link', 'href']
            }

            order = {}
            missing_fields = []

            for target_field, source_fields in field_mapping.items():
                found = False
                for source_field in source_fields:
                    if source_field in item and item[source_field]:
                        order[target_field] = item[source_field]
                        found = True
                        break

                if not found:
                    order[target_field] = ""
                    if target_field in ['title', 'order_id']:
                        missing_fields.append(target_field)

            # 标准化价格字段
            for price_field in ['price', 'total_amount']:
                if price_field in order:
                    original_value = order[price_field]
                    if isinstance(original_value, (int, float)):
                        order[price_field] = float(original_value)
                    elif isinstance(original_value, str):
                        # 移除货币符号和逗号
                        price_str = re.sub(r'[^\d.]', '', str(original_value))
                        order[price_field] = float(price_str) if price_str else 0.0
                    else:
                        order[price_field] = 0.0
                        print(f"⚠️ 价格字段类型异常: {price_field} = "
                              f"{original_value} ({type(original_value)})")

            # 标准化数量字段
            if 'quantity' in order:
                quantity_value = order['quantity']
                if isinstance(quantity_value, (int, float)):
                    order['quantity'] = int(quantity_value)
                elif isinstance(quantity_value, str):
                    try:
                        order['quantity'] = int(float(quantity_value))
                    except (ValueError, TypeError):
                        order['quantity'] = 0
                        print(f"⚠️ 数量字段转换失败: {quantity_value}")
                else:
                    order['quantity'] = 0

            # 验证必需字段
            if not order.get('title'):
                print("⚠️ 缺少必需字段: title")
                return None

            # 验证数据合理性
            if order.get('price', 0) < 0:
                print(f"⚠️ 价格不能为负数: {order.get('price')}")
                order['price'] = 0

            if order.get('quantity', 0) < 0:
                print(f"⚠️ 数量不能为负数: {order.get('quantity')}")
                order['quantity'] = 0

            if order.get('total_amount', 0) < 0:
                print(f"⚠️ 总金额不能为负数: {order.get('total_amount')}")
                order['total_amount'] = 0

            # 计算总金额验证
            if order.get('price') and order.get('quantity'):
                calculated_total = order['price'] * order['quantity']
                if abs(calculated_total - order.get('total_amount', 0)) > 0.01:
                    print(f"⚠️ 总金额计算不一致: 计算值={calculated_total}, "
                          f"实际值={order.get('total_amount')}")
                    # 以计算值为准
                    order['total_amount'] = calculated_total

            return order

        except (ValueError, TypeError, KeyError) as e:
            print(f"⚠️ 提取订单字段失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    async def extract_orders_from_dom(self, page) -> List[Dict[str, Any]]:
        """通过DOM解析抓取订单数据"""
        try:
            # 使用JavaScript提取订单数据
            js_code = """
            () => {
                const toText = el => (el?.textContent || "").trim();
                const toNumber = str => (
                    parseFloat(String(str).replace(/[^\\d.]/g, "")) || 0
                );

                // 尝试多种选择器
                const selectors = [
                    '.order-list .order-item',
                    '.order-item',
                    '[class*="order"] [class*="item"]',
                    '.trade-item',
                    '.list-item'
                ];

                let items = [];
                for (const selector of selectors) {
                    items = Array.from(document.querySelectorAll(selector));
                    if (items.length > 0) break;
                }

                return items.map(item => {
                    // 提取标题
                    const titleSelectors = [
                        '.title, .product-title, .goods-title, .product-name, .goods-name',
                        '[class*="title"], [class*="name"]'
                    ];
                    let title = "";
                    for (const selector of titleSelectors) {
                        const el = item.querySelector(selector);
                        if (el && toText(el)) {
                            title = toText(el);
                            break;
                        }
                    }

                    // 提取价格
                    const priceSelectors = [
                        '.price, .amount, .money, .unit-price',
                        '[class*="price"], [class*="amount"], [class*="money"]'
                    ];
                    let price = 0;
                    for (const selector of priceSelectors) {
                        const el = item.querySelector(selector);
                        if (el && toText(el)) {
                            price = toNumber(toText(el));
                            if (price > 0) break;
                        }
                    }

                    // 提取数量
                    const quantitySelectors = [
                        '.quantity, .num, .count, .number',
                        '[class*="quantity"], [class*="num"], [class*="count"]'
                    ];
                    let quantity = 0;
                    for (const selector of quantitySelectors) {
                        const el = item.querySelector(selector);
                        if (el && toText(el)) {
                            quantity = parseInt(toText(el)) || 0;
                            if (quantity > 0) break;
                        }
                    }

                    // 提取店铺名
                    const shopSelectors = [
                        '.shop-name, .seller-name, .store-name',
                        '[class*="shop"], [class*="seller"], [class*="store"]'
                    ];
                    let shopName = "";
                    for (const selector of shopSelectors) {
                        const el = item.querySelector(selector);
                        if (el && toText(el)) {
                            shopName = toText(el);
                            break;
                        }
                    }

                    // 提取订单时间
                    const timeSelectors = [
                        '.time, .date, .order-time, .create-time',
                        '[class*="time"], [class*="date"]'
                    ];
                    let orderTime = "";
                    for (const selector of timeSelectors) {
                        const el = item.querySelector(selector);
                        if (el && toText(el)) {
                            orderTime = toText(el);
                            break;
                        }
                    }

                    // 提取图片
                    const imgSelectors = [
                        'img[src*="img"], img[src*="photo"], img[src*="pic"]',
                        '.product-img img, .goods-img img, .item-img img'
                    ];
                    let imageUrl = "";
                    for (const selector of imgSelectors) {
                        const el = item.querySelector(selector);
                        if (el && el.src) {
                            imageUrl = el.src;
                            break;
                        }
                    }

                    return {
                        title: title,
                        price: price,
                        quantity: quantity,
                        shop_name: shopName,
                        order_time: orderTime,
                        image_url: imageUrl,
                        total_amount: price * quantity,
                        detail_url: "",
                        order_id: "",
                        status: ""
                    };
                }).filter(item => item.title);
            }
            """

            # 执行JavaScript提取数据
            orders_data = await page.evaluate(js_code)
            self.stats['dom_success'] = len(orders_data)

            print(f"🔍 DOM解析成功，提取到 {len(orders_data)} 个订单")
            return orders_data

        except (ValueError, TypeError, KeyError) as e:
            print(f"❌ DOM解析失败: {e}")
            return []

    async def extract_orders_from_page(
            self,
            page,
            url: str
    ) -> List[Dict[str, Any]]:
        """从订单页面提取订单数据"""
        print(f"🌐 目标订单页面: {url}")

        all_orders = []

        try:
            # 隐藏水印
            await page.add_style_tag(
                content=".quark-watermark{display:none!important}"
            )

            # 检查当前页面是否已经是订单页面
            current_url = page.url
            print(f"📍 当前页面: {current_url}")

            # 如果不是订单页面，则导航到订单页面
            if 'trade-order-list' not in current_url and 'buyer_order_list' not in current_url:
                print("🔄 导航到订单页面...")
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        print(f"🔄 尝试导航 (第 {attempt + 1}/{max_retries} 次)...")
                        await page.goto(
                            url, timeout=30000, wait_until="domcontentloaded"
                        )

                        # 等待页面加载
                        await page.wait_for_load_state(
                            "networkidle", timeout=30000
                        )
                        await page.wait_for_timeout(2000)

                        # 检查页面是否加载成功
                        page_title = await page.title()
                        if page_title:
                            print(f"📄 页面标题: {page_title}")
                            break
                        else:
                            print("⚠️ 页面标题为空，重试...")
                            if attempt < max_retries - 1:
                                await page.wait_for_timeout(3000)
                                continue
                    except Exception as e:
                        print(f"⚠️ 导航失败 (第 {attempt + 1} 次): {e}")
                        if attempt < max_retries - 1:
                            await page.wait_for_timeout(3000)
                            continue
                        else:
                            raise
            else:
                print("✅ 已在订单页面，无需导航")

            # 根据参数决定是否进行多页抓取
            if hasattr(self, 'enable_all_pages') and self.enable_all_pages:
                all_orders = await self.extract_all_pages_orders(page)
            else:
                # 单页抓取（原有逻辑）
                all_orders = await self.extract_single_page_orders(page)

            return all_orders

        except (ValueError, TypeError, KeyError) as e:
            print(f"❌ 提取订单数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    async def extract_single_page_orders(self, page) -> List[Dict[str, Any]]:
        """提取单页订单数据（原有逻辑）"""
        # 监听API响应
        api_orders = await self.extract_orders_from_api(page)

        # 滚动触发懒加载
        print("📜 滚动页面触发懒加载...")
        await page.evaluate(
            "window.scrollTo(0, document.body.scrollHeight)"
        )
        await page.wait_for_timeout(1000)

        # 再次向上滚动，确保所有数据加载
        await page.evaluate("window.scrollTo(0, 0)")
        await page.wait_for_timeout(500)
        await page.evaluate(
            "window.scrollTo(0, document.body.scrollHeight)"
        )
        await page.wait_for_timeout(1000)

        # 如果API没有获取到数据，使用DOM解析
        if not api_orders:
            print("🔍 API未获取到数据，使用DOM解析...")
            dom_orders = await self.extract_orders_from_dom(page)

            if dom_orders:
                print(f"✅ DOM解析成功，提取到 {len(dom_orders)} 个订单")
                self.stats['dom_success'] = len(dom_orders)
                return dom_orders
            else:
                print("⚠️ DOM解析也失败")
                return []
        else:
            print(f"✅ API解析成功，提取到 {len(api_orders)} 个订单")
            self.stats['api_success'] = len(api_orders)
            return api_orders

    async def extract_all_pages_orders(self, page) -> List[Dict[str, Any]]:
        """提取所有页面的订单数据"""
        all_orders = []
        current_page = 1
        max_pages = getattr(self, 'max_pages', 10)  # 使用参数设置的最大页数

        print(f"🔄 开始多页数据抓取...")

        while current_page <= max_pages:
            print(f"\n📄 正在处理第 {current_page} 页...")

            # 监听API响应
            api_orders = await self.extract_orders_from_api(page)

            # 滚动触发懒加载
            print("📜 滚动页面触发懒加载...")
            await page.evaluate(
                "window.scrollTo(0, document.body.scrollHeight)"
            )
            await page.wait_for_timeout(1000)

            # 再次向上滚动，确保所有数据加载
            await page.evaluate("window.scrollTo(0, 0)")
            await page.wait_for_timeout(500)
            await page.evaluate(
                "window.scrollTo(0, document.body.scrollHeight)"
            )
            await page.wait_for_timeout(1000)

            # 提取当前页数据
            page_orders = []
            if api_orders:
                page_orders = api_orders
                print(f"✅ API解析成功，提取到 {len(page_orders)} 个订单")
                self.stats['api_success'] += len(page_orders)
            else:
                print("🔍 API未获取到数据，使用DOM解析...")
                page_orders = await self.extract_orders_from_dom(page)
                if page_orders:
                    print(f"✅ DOM解析成功，提取到 {len(page_orders)} 个订单")
                    self.stats['dom_success'] += len(page_orders)
                else:
                    print("⚠️ 当前页面未提取到数据")

            # 如果当前页没有数据，可能已到最后一页
            if not page_orders:
                print(f"📄 第 {current_page} 页无数据，可能已到最后一页")
                break

            all_orders.extend(page_orders)
            print(f"📊 累计提取 {len(all_orders)} 个订单")

            # 尝试翻到下一页
            has_next_page = await self.go_to_next_page(page)
            if not has_next_page:
                print("📄 已到最后一页，抓取完成")
                break

            current_page += 1

            # 等待页面加载
            await page.wait_for_timeout(2000)

        print(f"🎉 多页抓取完成，总共提取 {len(all_orders)} 个订单")
        return all_orders

    async def go_to_next_page(self, page) -> bool:
        """尝试翻到下一页"""
        try:
            # 多种下一页按钮选择器
            next_page_selectors = [
                'a[title="下一页"]',
                'a:has-text("下一页")',
                'button:has-text("下一页")',
                '.pagination .next',
                '.page-next',
                '[class*="next"]:has-text("下一页")',
                '[class*="pagination"] a:last-child',
                '.ant-pagination-next',
                '.el-pagination__next'
            ]

            for selector in next_page_selectors:
                try:
                    next_button = page.locator(selector).first
                    if await next_button.is_visible() and await next_button.is_enabled():
                        print(f"🔄 找到下一页按钮: {selector}")
                        await next_button.click()
                        await page.wait_for_load_state("networkidle", timeout=10000)
                        return True
                except Exception:
                    continue

            # 如果没找到下一页按钮，尝试通过URL参数翻页
            current_url = page.url
            if 'page=' in current_url:
                import re
                # 提取当前页码
                page_match = re.search(r'page=(\d+)', current_url)
                if page_match:
                    current_page_num = int(page_match.group(1))
                    next_page_num = current_page_num + 1
                    next_url = re.sub(r'page=\d+', f'page={next_page_num}', current_url)
                    print(f"🔄 通过URL翻页: page={next_page_num}")
                    await page.goto(next_url, wait_until="networkidle")
                    return True

            print("⚠️ 未找到下一页按钮或已到最后一页")
            return False

        except Exception as e:
            print(f"⚠️ 翻页失败: {e}")
            return False

    async def save_to_excel(
            self,
            orders: List[Dict[str, Any]],
            output_path: str
    ):
        """保存订单数据到Excel"""
        try:
            if not orders:
                print("⚠️ 没有订单数据可保存")
                return

            # 数据验证
            valid_orders = []
            for i, order in enumerate(orders):
                if not isinstance(order, dict):
                    print(f"⚠️ 订单 {i} 不是字典类型，跳过")
                    continue

                if not order.get('title'):
                    print(f"⚠️ 订单 {i} 缺少标题，跳过")
                    continue

                valid_orders.append(order)

            if not valid_orders:
                print("⚠️ 没有有效的订单数据可保存")
                return

            print(f"📊 有效订单数量: {len(valid_orders)}/{len(orders)}")

            df = pd.DataFrame(valid_orders)

            # 重命名列为中文
            column_mapping = {
                'order_id': '订单号',
                'title': '商品名称',
                'price': '单价',
                'quantity': '数量',
                'total_amount': '总金额',
                'shop_name': '店铺名称',
                'order_time': '下单时间',
                'status': '订单状态',
                'image_url': '商品图片',
                'detail_url': '详情链接'
            }

            # 确保所有列都存在
            for target_col in column_mapping.values():
                if target_col not in df.columns:
                    df[target_col] = ""

            df = df.rename(columns=column_mapping)

            # 创建输出目录
            import os
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 保存到Excel
            df.to_excel(output_path, index=False)
            print(f"✅ 订单数据已保存到: {output_path}")
            print(f"📊 共 {len(valid_orders)} 个订单")

            # 显示数据统计
            print("\n📋 数据统计:")
            print(f"  订单号: {df['订单号'].count()} 个")
            print(f"  店铺数量: {df['店铺名称'].nunique()} 个")
            try:
                total_sum = float(df['总金额'].sum())
                if total_sum != 0:
                    print(f"  总金额: {total_sum:.2f} 元")
                else:
                    print("  总金额: 0.00 元")
            except (ValueError, TypeError):
                print("  总金额: 计算失败")

        except (ValueError, TypeError, KeyError) as e:
            print(f"❌ 保存Excel失败: {e}")
            import traceback
            traceback.print_exc()

    def print_stats(self):
        """打印统计信息"""
        print("\n📊 抓取统计:")
        print(f"  总订单数: {self.stats['total_orders']}")
        print(f"  API成功: {self.stats['api_success']}")
        print(f"  DOM成功: {self.stats['dom_success']}")
        print(f"  失败: {self.stats['failed']}")


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='1688订单数据抓取工具')
    parser.add_argument(
        '--url', '-u',
        default='https://air.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html?tradeStatus=waitbuyerreceive&spm=a260k.home2025.topmenu.dmyorder&page=1&pageSize=10',
        help='订单页面URL'
    )
    parser.add_argument('--output', '-o', help='输出Excel文件路径（默认：自动生成）')
    parser.add_argument('--headless', action='store_true', help='无头模式运行')
    parser.add_argument('--all-pages', action='store_true', help='抓取所有页面的订单数据')
    parser.add_argument('--max-pages', type=int, default=10, help='最大抓取页数（默认：10页）')

    args = parser.parse_args()

    # 设置输出路径
    if not args.output:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        args.output = PROJECT_ROOT / "reports" / f"1688_orders_{timestamp}.xlsx"

    print("🚀 开始1688订单数据抓取...")
    print(f"目标URL: {args.url}")
    print(f"输出文件: {args.output}")

    # 确保输出路径是字符串
    output_path_str = str(args.output)

    # 创建输出目录
    import os
    os.makedirs(os.path.dirname(output_path_str), exist_ok=True)

    extractor = OrderDataExtractor()

    # 设置多页抓取参数
    extractor.enable_all_pages = args.all_pages
    extractor.max_pages = args.max_pages

    try:
        # 连接到Chrome调试实例
        debug_port = 9222
        print("🔗 正在连接到Chrome调试实例...")

        # 检查Chrome调试接口
        try:
            version_url = f"http://localhost:{debug_port}/json/version"
            proxies = {'http': None, 'https': None}
            response = requests.get(version_url, timeout=5, proxies=proxies)
            if response.status_code != 200:
                print(f"❌ Chrome调试接口不可用，状态码: {response.status_code}")
                print("💡 请先运行 start_debug_chrome.bat 启动Chrome调试模式")
                return

            version_info = response.json()
            ws_url = version_info.get('webSocketDebuggerUrl')
            if not ws_url:
                print("❌ 无法获取WebSocket URL")
                return

            print(f"✅ Chrome调试接口正常: {version_info.get('Browser', 'Unknown')}")

        except Exception as e:
            print(f"❌ Chrome调试接口连接失败: {e}")
            print("💡 请确保Chrome调试模式正在运行")
            return

        # 使用Playwright连接到Chrome
        async with async_playwright() as p:
            print(f"🔌 连接到WebSocket: {ws_url}")
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例!")

            # 查找1688页面
            page = None
            if browser.contexts and browser.contexts[0].pages:
                for p in browser.contexts[0].pages:
                    if '1688.com' in p.url:
                        page = p
                        print(f"📄 找到1688页面: {p.url}")
                        break

            if not page:
                print("❌ 未找到1688页面")
                print("💡 请在Chrome中打开1688网站并登录")
                await browser.close()
                return

            # 提取订单数据
            print(f"🚀 开始提取订单数据...")
            orders = await extractor.extract_orders_from_page(page, args.url)
            extractor.stats['total_orders'] = len(orders)

            # 保存数据
            if orders:
                await extractor.save_to_excel(orders, output_path_str)
                print(f"💾 数据已保存到: {output_path_str}")
            else:
                print("⚠️ 未提取到订单数据")

            # 打印统计
            extractor.print_stats()

            # 关闭连接
            await browser.close()

            print("\n🎉 订单数据抓取完成!")
            print(f"📄 输出文件: {output_path_str}")

    except (ValueError, TypeError, KeyError) as e:
        print(f"❌ 抓取失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())