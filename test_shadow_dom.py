#!/usr/bin/env python3
"""
测试Shadow DOM中的订单数据抓取
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from playwright.async_api import async_playwright
import requests

async def test_shadow_dom_orders():
    """测试Shadow DOM中的订单抓取"""
    
    print("🔍 测试Shadow DOM中的订单数据抓取")
    print("=" * 50)
    
    # 检查Chrome调试接口
    debug_port = 9222
    try:
        version_url = f"http://localhost:{debug_port}/json/version"
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        if response.status_code != 200:
            print(f"❌ Chrome调试接口不可用，状态码: {response.status_code}")
            return
        
        version_info = response.json()
        ws_url = version_info.get('webSocketDebuggerUrl')
        if not ws_url:
            print("❌ 无法获取WebSocket URL")
            return
        
        print(f"✅ Chrome调试接口正常: {version_info.get('Browser', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ Chrome调试接口连接失败: {e}")
        return
    
    # 连接到Chrome
    async with async_playwright() as p:
        try:
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例!")
            
            # 查找1688页面
            page = None
            if browser.contexts and browser.contexts[0].pages:
                for p_page in browser.contexts[0].pages:
                    if '1688.com' in p_page.url:
                        page = p_page
                        print(f"📄 找到1688页面: {p_page.url}")
                        break
            
            if not page:
                print("❌ 未找到1688页面")
                return
            
            # 测试Shadow DOM
            await test_shadow_dom_structure(page)
            
            # 测试Playwright的:light()选择器
            await test_playwright_light_selector(page)
            
            # 测试JavaScript Shadow DOM穿透
            await test_js_shadow_penetration(page)
            
            await browser.close()
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")

async def test_shadow_dom_structure(page):
    """测试Shadow DOM结构"""
    print("\n🔍 测试Shadow DOM结构")
    print("-" * 30)
    
    try:
        # 检查页面中的Shadow DOM
        shadow_info = await page.evaluate("""
            () => {
                const allElements = document.querySelectorAll('*');
                const shadowHosts = [];
                
                for (const el of allElements) {
                    if (el.shadowRoot) {
                        shadowHosts.push({
                            tagName: el.tagName,
                            className: el.className,
                            id: el.id,
                            shadowRootMode: el.shadowRoot.mode
                        });
                    }
                }
                
                return {
                    totalElements: allElements.length,
                    shadowHosts: shadowHosts
                };
            }
        """)
        
        print(f"📊 页面元素统计:")
        print(f"   总元素数: {shadow_info['totalElements']}")
        print(f"   Shadow DOM宿主: {len(shadow_info['shadowHosts'])} 个")
        
        if shadow_info['shadowHosts']:
            print("   📋 Shadow DOM宿主元素:")
            for i, host in enumerate(shadow_info['shadowHosts'][:5]):  # 只显示前5个
                print(f"      {i+1}. {host['tagName']}.{host['className']} #{host['id']} (mode: {host['shadowRootMode']})")
        
    except Exception as e:
        print(f"❌ 测试Shadow DOM结构失败: {e}")

async def test_playwright_light_selector(page):
    """测试Playwright的:light()选择器"""
    print("\n🔍 测试Playwright的:light()选择器")
    print("-" * 30)
    
    try:
        # 测试各种Shadow DOM选择器
        selectors = [
            'order-item',  # 直接尝试
            'order-item[data-tracker]',
            '[data-tracker*="order"]',
            '.order-list-container',
            # Playwright可能需要不同的语法
            '>>> order-item',  # 深度选择器
            '>>> order-item[data-tracker]',
            '>>> [data-tracker*="order"]'
        ]
        
        for selector in selectors:
            try:
                elements = await page.query_selector_all(selector)
                print(f"   {selector}: {len(elements)} 个元素")
                
                if elements and len(elements) > 0:
                    # 获取第一个元素的信息
                    first_elem = elements[0]
                    tag_name = await first_elem.evaluate('el => el.tagName')
                    text_content = await first_elem.text_content()
                    data_tracker = await first_elem.get_attribute('data-tracker')
                    
                    print(f"      第一个元素: {tag_name}")
                    if data_tracker:
                        print(f"      data-tracker: {data_tracker[:50]}...")
                    if text_content:
                        preview = text_content.strip()[:100].replace('\n', ' ')
                        print(f"      文本预览: {preview}...")
                        
            except Exception as e:
                print(f"   ❌ {selector}: 查询失败 - {e}")
        
    except Exception as e:
        print(f"❌ 测试Playwright选择器失败: {e}")

async def test_js_shadow_penetration(page):
    """测试JavaScript Shadow DOM穿透"""
    print("\n🔍 测试JavaScript Shadow DOM穿透")
    print("-" * 30)
    
    try:
        result = await page.evaluate("""
            () => {
                // Shadow DOM 穿透函数
                function querySelectorDeep(selector, root = document) {
                    const elements = [];
                    
                    // 在当前层级查找
                    const found = root.querySelectorAll(selector);
                    elements.push(...found);
                    
                    // 递归查找所有 Shadow DOM
                    const allElements = root.querySelectorAll('*');
                    for (const el of allElements) {
                        if (el.shadowRoot) {
                            const shadowElements = querySelectorDeep(selector, el.shadowRoot);
                            elements.push(...shadowElements);
                        }
                    }
                    
                    return elements;
                }
                
                // 测试各种选择器
                const selectors = [
                    'order-item',
                    'order-item[data-tracker]',
                    '[data-tracker*="order"]',
                    '.order-list-container'
                ];
                
                const results = {};
                
                for (const selector of selectors) {
                    const normalElements = document.querySelectorAll(selector);
                    const deepElements = querySelectorDeep(selector);
                    
                    results[selector] = {
                        normal: normalElements.length,
                        deep: deepElements.length,
                        sampleData: deepElements.length > 0 ? {
                            tagName: deepElements[0].tagName,
                            dataTracker: deepElements[0].getAttribute('data-tracker'),
                            textLength: deepElements[0].textContent.trim().length
                        } : null
                    };
                }
                
                return results;
            }
        """)
        
        print("📊 JavaScript穿透结果:")
        for selector, data in result.items():
            print(f"   {selector}:")
            print(f"      普通查找: {data['normal']} 个")
            print(f"      深度查找: {data['deep']} 个")
            if data['sampleData']:
                sample = data['sampleData']
                print(f"      样本: {sample['tagName']} (data-tracker: {sample['dataTracker'][:30] if sample['dataTracker'] else 'None'}...)")
                print(f"      文本长度: {sample['textLength']} 字符")
        
    except Exception as e:
        print(f"❌ 测试JavaScript穿透失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_shadow_dom_orders())
