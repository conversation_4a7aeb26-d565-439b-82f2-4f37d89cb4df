#!/usr/bin/env python3
"""
调试订单数据抓取问题
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from playwright.async_api import async_playwright
import requests

async def debug_order_page():
    """调试订单页面数据抓取"""
    
    print("🔍 开始调试订单页面数据抓取")
    print("=" * 50)
    
    # 检查Chrome调试接口
    debug_port = 9222
    try:
        version_url = f"http://localhost:{debug_port}/json/version"
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        if response.status_code != 200:
            print(f"❌ Chrome调试接口不可用，状态码: {response.status_code}")
            return
        
        version_info = response.json()
        ws_url = version_info.get('webSocketDebuggerUrl')
        if not ws_url:
            print("❌ 无法获取WebSocket URL")
            return
        
        print(f"✅ Chrome调试接口正常: {version_info.get('Browser', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ Chrome调试接口连接失败: {e}")
        return
    
    # 连接到Chrome
    async with async_playwright() as p:
        try:
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例!")
            
            # 查找1688页面
            page = None
            if browser.contexts and browser.contexts[0].pages:
                for p_page in browser.contexts[0].pages:
                    if '1688.com' in p_page.url:
                        page = p_page
                        print(f"📄 找到1688页面: {p_page.url}")
                        break
            
            if not page:
                print("❌ 未找到1688页面")
                return
            
            # 调试页面内容
            await debug_page_content(page)
            
            # 调试网络请求
            await debug_network_requests(page)
            
            # 调试DOM结构
            await debug_dom_structure(page)
            
            await browser.close()
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")

async def debug_page_content(page):
    """调试页面基本内容"""
    print("\n🔍 调试页面基本内容")
    print("-" * 30)
    
    try:
        # 获取页面基本信息
        url = page.url
        title = await page.title()
        
        print(f"📍 当前URL: {url}")
        print(f"📄 页面标题: {title}")
        
        # 检查页面是否完全加载
        ready_state = await page.evaluate("document.readyState")
        print(f"📊 页面状态: {ready_state}")
        
        # 检查页面内容长度
        content_length = await page.evaluate("document.body.innerHTML.length")
        print(f"📏 页面内容长度: {content_length} 字符")
        
        # 检查是否有错误信息
        error_elements = await page.query_selector_all('[class*="error"], [class*="empty"], [class*="no-data"]')
        if error_elements:
            print(f"⚠️ 发现 {len(error_elements)} 个可能的错误元素")
            for i, elem in enumerate(error_elements[:3]):  # 只显示前3个
                text = await elem.text_content()
                if text and text.strip():
                    print(f"   错误 {i+1}: {text.strip()}")
        
    except Exception as e:
        print(f"❌ 调试页面内容失败: {e}")

async def debug_network_requests(page):
    """调试网络请求"""
    print("\n🔍 调试网络请求")
    print("-" * 30)
    
    captured_requests = []
    captured_responses = []
    
    def handle_request(request):
        if any(keyword in request.url for keyword in ['/order', '/trade', '/buyer', '/list']):
            captured_requests.append({
                'url': request.url,
                'method': request.method,
                'resource_type': request.resource_type
            })
            print(f"📤 请求: {request.method} {request.url}")
    
    def handle_response(response):
        if any(keyword in response.url for keyword in ['/order', '/trade', '/buyer', '/list']):
            captured_responses.append({
                'url': response.url,
                'status': response.status,
                'resource_type': response.request.resource_type
            })
            print(f"📥 响应: {response.status} {response.url}")
    
    # 设置监听器
    page.on("request", handle_request)
    page.on("response", handle_response)
    
    try:
        # 刷新页面触发网络请求
        print("🔄 刷新页面以触发网络请求...")
        await page.reload(wait_until="networkidle")
        await page.wait_for_timeout(3000)
        
        print(f"\n📊 网络请求统计:")
        print(f"   捕获请求: {len(captured_requests)} 个")
        print(f"   捕获响应: {len(captured_responses)} 个")
        
        # 显示详细信息
        if captured_responses:
            print("\n📋 响应详情:")
            for resp in captured_responses[:5]:  # 只显示前5个
                print(f"   {resp['status']} - {resp['url']}")
        else:
            print("⚠️ 没有捕获到相关的网络响应")
        
    except Exception as e:
        print(f"❌ 调试网络请求失败: {e}")

async def debug_dom_structure(page):
    """调试DOM结构"""
    print("\n🔍 调试DOM结构")
    print("-" * 30)
    
    try:
        # 检查常见的订单容器
        selectors_to_check = [
            '.order-list',
            '.order-item',
            '[class*="order"]',
            '[class*="trade"]',
            '.list-item',
            '[class*="list"]',
            'table',
            'tbody tr',
            '[class*="row"]',
            '[class*="card"]'
        ]
        
        print("📋 检查常见选择器:")
        for selector in selectors_to_check:
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    print(f"   ✅ {selector}: {len(elements)} 个元素")
                    
                    # 获取第一个元素的文本内容（如果有的话）
                    if len(elements) > 0:
                        first_text = await elements[0].text_content()
                        if first_text and first_text.strip():
                            preview = first_text.strip()[:100]
                            print(f"      预览: {preview}...")
                else:
                    print(f"   ❌ {selector}: 0 个元素")
            except Exception as e:
                print(f"   ⚠️ {selector}: 检查失败 - {e}")
        
        # 检查页面是否有数据
        print("\n📊 页面数据检查:")
        
        # 检查是否有"暂无数据"类似的提示
        no_data_selectors = [
            ':text("暂无")',
            ':text("没有")',
            ':text("空")',
            '[class*="empty"]',
            '[class*="no-data"]'
        ]
        
        for selector in no_data_selectors:
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    print(f"   ⚠️ 发现空数据提示: {selector} ({len(elements)} 个)")
                    for elem in elements[:2]:
                        text = await elem.text_content()
                        if text and text.strip():
                            print(f"      内容: {text.strip()}")
            except:
                pass
        
        # 尝试执行JavaScript获取更多信息
        print("\n🔧 JavaScript调试:")
        
        # 检查页面中的所有文本内容
        all_text = await page.evaluate("document.body.textContent")
        if "订单" in all_text:
            print("   ✅ 页面包含'订单'关键字")
        else:
            print("   ⚠️ 页面不包含'订单'关键字")
        
        if "暂无" in all_text or "没有" in all_text:
            print("   ⚠️ 页面可能显示无数据状态")
        
        # 检查是否有加载中的状态
        loading_indicators = await page.query_selector_all('[class*="loading"], [class*="spinner"]')
        if loading_indicators:
            print(f"   🔄 发现 {len(loading_indicators)} 个加载指示器")
        
    except Exception as e:
        print(f"❌ 调试DOM结构失败: {e}")

if __name__ == "__main__":
    asyncio.run(debug_order_page())
