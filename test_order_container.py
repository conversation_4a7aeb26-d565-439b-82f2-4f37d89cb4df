#!/usr/bin/env python3
"""
测试基于 .order-list-container 的订单数据抓取
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from playwright.async_api import async_playwright
import requests

async def test_order_container():
    """测试订单容器的抓取功能"""
    
    print("🔍 测试基于 .order-list-container 的订单抓取")
    print("=" * 50)
    
    # 检查Chrome调试接口
    debug_port = 9222
    try:
        version_url = f"http://localhost:{debug_port}/json/version"
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        if response.status_code != 200:
            print(f"❌ Chrome调试接口不可用，状态码: {response.status_code}")
            print("💡 请先运行 start_debug_chrome.bat 启动Chrome调试模式")
            return
        
        version_info = response.json()
        ws_url = version_info.get('webSocketDebuggerUrl')
        if not ws_url:
            print("❌ 无法获取WebSocket URL")
            return
        
        print(f"✅ Chrome调试接口正常: {version_info.get('Browser', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ Chrome调试接口连接失败: {e}")
        return
    
    # 连接到Chrome
    async with async_playwright() as p:
        try:
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例!")
            
            # 查找1688页面
            page = None
            if browser.contexts and browser.contexts[0].pages:
                for p_page in browser.contexts[0].pages:
                    if '1688.com' in p_page.url:
                        page = p_page
                        print(f"📄 找到1688页面: {p_page.url}")
                        break
            
            if not page:
                print("❌ 未找到1688页面")
                print("💡 请在Chrome中打开1688网站并登录到订单页面")
                return
            
            # 测试订单容器
            await test_container_structure(page)
            
            # 直接检查页面HTML
            await check_page_html(page)

            # 测试改进的JavaScript代码
            await test_improved_js_extraction(page)

            # 测试翻页容器
            await test_pagination_container(page)

            await browser.close()
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")

async def check_page_html(page):
    """直接检查页面HTML"""
    print("\n🔍 直接检查页面HTML")
    print("-" * 30)

    try:
        # 获取页面HTML并查找订单容器
        html_content = await page.content()

        if 'order-list-container' in html_content:
            print("✅ HTML中包含 'order-list-container'")

            # 提取包含order-list-container的行
            lines = html_content.split('\n')
            container_lines = [line.strip() for line in lines if 'order-list-container' in line]

            print(f"📋 找到 {len(container_lines)} 行包含 'order-list-container':")
            for i, line in enumerate(container_lines[:3]):  # 只显示前3行
                print(f"   行 {i+1}: {line[:100]}...")
        else:
            print("❌ HTML中不包含 'order-list-container'")

            # 查找其他可能的容器
            possible_containers = ['order-list', 'order-container', 'list-container', 'trade-list']
            for container in possible_containers:
                if container in html_content:
                    print(f"✅ HTML中包含 '{container}'")
                    break
            else:
                print("❌ HTML中不包含任何已知的订单容器")

        # 检查页面是否完全加载
        if 'loading' in html_content.lower() or 'spinner' in html_content.lower():
            print("⚠️ 页面可能仍在加载中")

    except Exception as e:
        print(f"❌ 检查页面HTML失败: {e}")

async def test_container_structure(page):
    """测试订单容器结构"""
    print("\n🔍 测试订单容器结构")
    print("-" * 30)
    
    try:
        # 检查订单容器
        container = await page.query_selector('.order-list-container')
        if container:
            print("✅ 找到 .order-list-container")
            
            # 获取容器信息
            container_text = await container.text_content()
            print(f"📏 容器内容长度: {len(container_text)} 字符")
            
            # 检查容器内的直接子元素
            children = await page.evaluate("""
                () => {
                    const container = document.querySelector('.order-list-container');
                    if (!container) return [];
                    
                    return Array.from(container.children).map((child, index) => ({
                        index: index,
                        tagName: child.tagName,
                        className: child.className,
                        textLength: child.textContent.trim().length,
                        hasPrice: child.textContent.includes('￥') || child.textContent.includes('¥') || child.textContent.includes('元')
                    }));
                }
            """)
            
            print(f"📊 容器直接子元素: {len(children)} 个")
            for child in children[:5]:  # 只显示前5个
                print(f"   元素 {child['index']}: {child['tagName']}.{child['className'][:30]}... "
                      f"(文本长度: {child['textLength']}, 包含价格: {child['hasPrice']})")
            
            # 检查订单项选择器 - 基于实际HTML结构
            selectors = [
                'order-item',                                    # 直接的order-item标签
                'order-item[data-tracker]',                      # 带data-tracker的order-item
                '[data-tracker*="order"]',                       # 包含order的data-tracker
                '.order-list-container order-item',             # 容器内的order-item
                '.order-list-container order-item[data-tracker]', # 容器内带data-tracker的order-item
                '.order-list-container [data-tracker*="order"]', # 容器内包含order的data-tracker
                '.order-list-container .order-item',            # 传统CSS类（备用）
                '.order-list-container [class*="order-item"]',
                '.order-list-container [class*="item"]'
            ]
            
            print("\n📋 测试订单项选择器:")
            for selector in selectors:
                elements = await page.query_selector_all(selector)
                print(f"   {selector}: {len(elements)} 个元素")
                
                if elements and len(elements) > 0:
                    # 获取第一个元素的详细信息
                    first_elem_info = await page.evaluate(f"""
                        () => {{
                            const elem = document.querySelector('{selector}');
                            if (!elem) return null;
                            
                            return {{
                                tagName: elem.tagName,
                                className: elem.className,
                                textContent: elem.textContent.trim().substring(0, 100),
                                hasPrice: elem.textContent.includes('￥') || elem.textContent.includes('¥') || elem.textContent.includes('元'),
                                childrenCount: elem.children.length
                            }};
                        }}
                    """)
                    
                    if first_elem_info:
                        print(f"      第一个元素: {first_elem_info['tagName']}.{first_elem_info['className'][:20]}...")
                        print(f"      文本预览: {first_elem_info['textContent'][:50]}...")
                        print(f"      包含价格: {first_elem_info['hasPrice']}")
                        print(f"      子元素数: {first_elem_info['childrenCount']}")
        else:
            print("❌ 未找到 .order-list-container")
            
            # 检查备用容器
            backup_selectors = ['.order-list', '[class*="order-list"]', '[class*="order"][class*="container"]']
            for backup in backup_selectors:
                backup_elem = await page.query_selector(backup)
                if backup_elem:
                    print(f"✅ 找到备用容器: {backup}")
                    break
            
    except Exception as e:
        print(f"❌ 测试容器结构失败: {e}")

async def test_improved_js_extraction(page):
    """测试改进的JavaScript提取代码"""
    print("\n🔍 测试改进的JavaScript提取")
    print("-" * 30)
    
    try:
        # 执行改进的JavaScript代码
        result = await page.evaluate("""
            () => {
                const toText = el => (el?.textContent || "").trim();
                const toNumber = str => (
                    parseFloat(String(str).replace(/[^\\d.]/g, "")) || 0
                );

                console.log("🔍 开始DOM解析...");

                // 调试：列出所有可能的容器
                const allContainers = document.querySelectorAll('[class*="order"], [class*="list"], [class*="container"]');
                console.log("所有可能的容器:", allContainers.length);

                // 首先查找订单列表容器 - 尝试多种方式
                let orderContainer = document.querySelector('.order-list-container');
                console.log("方式1 (.order-list-container):", orderContainer ? "✅ 找到" : "❌ 未找到");

                if (!orderContainer) {
                    orderContainer = document.querySelector('div.order-list-container');
                    console.log("方式2 (div.order-list-container):", orderContainer ? "✅ 找到" : "❌ 未找到");
                }

                if (!orderContainer) {
                    orderContainer = document.querySelector('[class="order-list-container"]');
                    console.log("方式3 ([class=\"order-list-container\"]):", orderContainer ? "✅ 找到" : "❌ 未找到");
                }

                if (!orderContainer) {
                    // 查找包含order-list-container的元素
                    const elements = document.querySelectorAll('*');
                    for (let el of elements) {
                        if (el.className && el.className.includes('order-list-container')) {
                            orderContainer = el;
                            console.log("方式4 (遍历查找):", "✅ 找到");
                            break;
                        }
                    }
                }

                if (!orderContainer) {
                    console.log("所有方式都未找到订单容器");
                    // 返回调试信息
                    const debugInfo = Array.from(allContainers).slice(0, 10).map(el => ({
                        tagName: el.tagName,
                        className: el.className,
                        id: el.id
                    }));
                    return {
                        error: "未找到订单容器",
                        debugContainers: debugInfo,
                        totalContainers: allContainers.length
                    };
                }

                // 在容器内查找订单项 - 基于实际HTML结构
                const selectors = [
                    'order-item',                                    // 直接的order-item标签
                    'order-item[data-tracker]',                      // 带data-tracker的order-item
                    '[data-tracker*="order"]',                       // 包含order的data-tracker
                    '.order-list-container order-item',             // 容器内的order-item
                    '.order-list-container order-item[data-tracker]', // 容器内带data-tracker的order-item
                    '.order-list-container [data-tracker*="order"]'  // 容器内包含order的data-tracker
                ];

                let items = [];
                let usedSelector = '';
                
                for (const selector of selectors) {
                    const found = Array.from(document.querySelectorAll(selector));
                    console.log(`选择器 ${selector}: 找到 ${found.length} 个元素`);
                    if (found.length > 0) {
                        items = found;
                        usedSelector = selector;
                        break;
                    }
                }
                
                if (items.length === 0) {
                    // 分析容器内的直接子元素
                    const directChildren = Array.from(orderContainer.children);
                    console.log(`订单容器直接子元素数量: ${directChildren.length}`);
                    
                    const possibleItems = directChildren.filter(el => {
                        const text = toText(el);
                        const hasPrice = text.includes('￥') || text.includes('¥') || text.includes('元');
                        const hasNumbers = /\\d+/.test(text);
                        const hasReasonableLength = text.length > 20 && text.length < 2000;
                        return hasPrice && hasNumbers && hasReasonableLength;
                    });
                    
                    items = possibleItems;
                    usedSelector = '直接子元素分析';
                }

                return {
                    containerFound: !!orderContainer,
                    usedSelector: usedSelector,
                    itemsFound: items.length,
                    sampleTexts: items.slice(0, 3).map(item => toText(item).substring(0, 100))
                };
            }
        """)
        
        print(f"📊 JavaScript执行结果:")

        if result.get('error'):
            print(f"   ❌ 错误: {result['error']}")
            if result.get('debugContainers'):
                print(f"   📋 找到的其他容器 ({result.get('totalContainers', 0)} 个):")
                for container in result['debugContainers']:
                    print(f"      {container['tagName']}.{container['className']} #{container['id']}")
        else:
            print(f"   订单容器: {'✅ 找到' if result.get('containerFound') else '❌ 未找到'}")
            print(f"   使用选择器: {result.get('usedSelector', '无')}")
            print(f"   找到订单项: {result.get('itemsFound', 0)} 个")

            if result.get('sampleTexts'):
                print("   📋 样本文本:")
                for i, text in enumerate(result['sampleTexts']):
                    print(f"      样本 {i+1}: {text}...")
        
    except Exception as e:
        print(f"❌ 测试JavaScript提取失败: {e}")

async def test_pagination_container(page):
    """测试翻页容器"""
    print("\n🔍 测试翻页容器")
    print("-" * 30)

    try:
        # 检查翻页容器
        page_container = await page.query_selector('.page#page, div.page#page')
        if page_container:
            print("✅ 找到翻页容器: .page#page")

            # 获取容器内容
            container_text = await page_container.text_content()
            print(f"📏 容器内容: {container_text.strip()}")

            # 检查容器内的翻页元素
            pagination_info = await page.evaluate("""
                () => {
                    const container = document.querySelector('.page#page') || document.querySelector('div.page#page');
                    if (!container) return null;

                    const elements = container.querySelectorAll('a, button, span');
                    const pageElements = Array.from(elements).map(el => ({
                        tagName: el.tagName,
                        text: el.textContent.trim(),
                        href: el.href || '',
                        className: el.className,
                        isVisible: el.offsetParent !== null,
                        disabled: el.disabled || false
                    }));

                    // 查找下一页元素
                    const nextPageElements = pageElements.filter(el =>
                        el.text.includes('下一页') || el.text.includes('下页') ||
                        el.text.includes('next') || el.text === '>' || el.text === '»'
                    );

                    return {
                        totalElements: pageElements.length,
                        allElements: pageElements,
                        nextPageElements: nextPageElements
                    };
                }
            """)

            if pagination_info:
                print(f"📊 翻页容器分析:")
                print(f"   总元素数: {pagination_info['totalElements']}")

                print("   📋 所有翻页元素:")
                for elem in pagination_info['allElements']:
                    if elem['text']:
                        status = "可见" if elem['isVisible'] else "隐藏"
                        disabled = "禁用" if elem['disabled'] else "启用"
                        print(f"      {elem['tagName']}: '{elem['text']}' ({status}, {disabled})")

                if pagination_info['nextPageElements']:
                    print("   🎯 下一页相关元素:")
                    for elem in pagination_info['nextPageElements']:
                        clickable = elem['isVisible'] and not elem['disabled']
                        print(f"      {elem['tagName']}: '{elem['text']}' (可点击: {clickable})")
                        if elem['href']:
                            print(f"         链接: {elem['href']}")
                else:
                    print("   ⚠️ 未找到下一页相关元素")
        else:
            print("❌ 未找到翻页容器 .page#page")

            # 检查其他可能的分页容器
            other_containers = await page.query_selector_all('.pagination, [class*="page"], [class*="pager"]')
            if other_containers:
                print(f"📋 找到其他分页容器: {len(other_containers)} 个")
                for i, container in enumerate(other_containers[:3]):
                    container_class = await container.get_attribute('class')
                    container_text = await container.text_content()
                    print(f"   容器 {i+1}: .{container_class}")
                    print(f"      内容: {container_text.strip()[:100]}...")
            else:
                print("❌ 未找到任何分页容器")

    except Exception as e:
        print(f"❌ 测试翻页容器失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_order_container())
